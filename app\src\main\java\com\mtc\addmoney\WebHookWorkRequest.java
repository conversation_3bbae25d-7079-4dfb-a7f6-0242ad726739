package com.mtc.addmoney;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import androidx.work.Worker;
import androidx.work.WorkerParameters;
import androidx.work.ListenableWorker;
import androidx.work.Data;
import androidx.work.Constraints;
import androidx.work.NetworkType;
import androidx.work.OneTimeWorkRequest;

import com.mtc.addmoney.ssl.TLSSocketFactory;

import org.apache.http.conn.ssl.AllowAllHostnameVerifier;
import org.json.JSONObject;

import java.io.BufferedWriter;
import java.io.OutputStreamWriter;
import java.io.BufferedOutputStream;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;

import javax.net.ssl.HttpsURLConnection;

public class WebHookWorkRequest extends Worker {

    public static final String DATA_URL = "URL";
    public static final String DATA_TEXT = "TEXT";
    public static final String DATA_HEADERS = "HEADERS";
    public static final String DATA_IGNORE_SSL = "IGNORE_SSL";

    public static final int MAX_ATTEMPT = 10;

    public static final String RESULT_SUCCESS = "success";
    public static final String RESULT_ERROR = "error";
    public static final String RESULT_RETRY = "error_retry";

    private static final String TAG = "SmsGateway";

    public WebHookWorkRequest(Context context, WorkerParameters workerParams) {
        super(context, workerParams);
    }

    @Override
    public ListenableWorker.Result doWork() {
        if (getRunAttemptCount() > MAX_ATTEMPT) {
            return ListenableWorker.Result.failure();
        }

        String url = getInputData().getString(DATA_URL);
        String jsonBody = getInputData().getString(DATA_TEXT);
        String headers = getInputData().getString(DATA_HEADERS);
        boolean ignoreSsl = getInputData().getBoolean(DATA_IGNORE_SSL, false);

        try {
            String result = makeRequest(url, jsonBody, headers, ignoreSsl);

            switch (result) {
                case RESULT_SUCCESS:
                    return ListenableWorker.Result.success();
                case RESULT_RETRY:
                    return ListenableWorker.Result.retry();
                default:
                    return ListenableWorker.Result.failure();
            }

        } catch (Exception e) {
            Log.e(TAG, "Fatal error: ", e);
            return ListenableWorker.Result.failure();
        }
    }

    private String makeRequest(String urlString, String jsonBody, String headersJson, boolean ignoreSsl) {
        HttpURLConnection connection = null;

        try {
            Log.i(TAG, "Sending request to: " + urlString);

            URL url = new URL(urlString);
            connection = (HttpURLConnection) url.openConnection();

            // Handle SSL
            if (connection instanceof HttpsURLConnection) {
                HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
                httpsConnection.setSSLSocketFactory(new TLSSocketFactory(ignoreSsl));
                if (ignoreSsl) {
                    httpsConnection.setHostnameVerifier(new AllowAllHostnameVerifier());
                }
            }

            // Setup connection
            connection.setDoOutput(true);
            connection.setChunkedStreamingMode(0);
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");

            // Apply custom headers
            JSONObject headerObj = new JSONObject(headersJson);
            Iterator<String> keys = headerObj.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                Object value = headerObj.get(key);
                if (value instanceof String) {
                    connection.setRequestProperty(key, (String) value);
                } else {
                    Log.w(TAG, "Skipping non-string header: " + key);
                }
            }

            // Write body
            try (
                    BufferedOutputStream outputStream = new BufferedOutputStream(connection.getOutputStream());
                    BufferedWriter writer = new BufferedWriter(
                            new OutputStreamWriter(outputStream, Build.VERSION.SDK_INT >= 19 ? StandardCharsets.UTF_8 : null)
                    )
            ) {
                writer.write(jsonBody);
                writer.flush();
            }

            // Optional: read response to trigger execution
            try (BufferedInputStream inputStream = new BufferedInputStream(connection.getInputStream())) {
                // Read or ignore, just to complete the request
                inputStream.read(); // Blocking call ensures stream is opened
            }

            int responseCode = connection.getResponseCode();
            Log.i(TAG, "Response code: " + responseCode);

            if (String.valueOf(responseCode).startsWith("2")) {
                return RESULT_SUCCESS;
            } else {
                return RESULT_ERROR;
            }

        } catch (IOException e) {
            Log.e(TAG, "Network error: ", e);
            return RESULT_RETRY;
        } catch (Exception e) {
            Log.e(TAG, "Request error: ", e);
            return RESULT_ERROR;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
}
